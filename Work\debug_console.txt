[Categories] Module loading...
categories.js?v=1751520785:29 [Categories] BackendModule is available for extension
categories.js?v=1751520785:30 [Categories] BackendModule type: object
categories.js?v=1751520785:31 [Categories] BackendModule object: {config: {…}, init: ƒ, initSidebar: ƒ, initModals: ƒ, initFilters: ƒ, …}
categories.js?v=1751520785:32 [Categories] Extending BackendModule with categories functionality...
categories.js?v=1751520785:1521 [Categories] BackendModule extended successfully
categories.js?v=1751520785:1526 [Categories] Module loaded
categories.js?v=1751520785:12 [Categories] DOMContentLoaded event fired
categories.js?v=1751520785:13 [Categories] Current page URL: https://theme25.rakla.bg/admin/index.php?route=catalog/category&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT
categories.js?v=1751520785:14 [Categories] Document ready state: interactive
categories.js?v=1751520785:18 [Categories] BackendModule found: {config: {…}, init: ƒ, initSidebar: ƒ, initModals: ƒ, initFilters: ƒ, …}
categories.js?v=1751520785:19 [Categories] Initializing categories...
categories.js?v=1751520785:45 [Categories] initCategories called
backend.js?v=1751386274:960 [BackendModule] Initializing categories module...
categories.js?v=1751520785:50 [Categories] Found category items: 15 NodeList(15) [div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…]
backend.js?v=1751386274:958 [BackendModule] Found category items: 15
categories.js?v=1751520785:53 [Categories] Setting up category event listeners...
categories.js?v=1751520785:56 [Categories] Initializing category drag and drop...
categories.js?v=1751520785:595 [Categories] initializeCategoryDragAndDrop called
categories.js?v=1751520785:598 [Categories] Found category rows: 15 NodeList(15) [div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…]
categories.js?v=1751520785:601 [Categories] Setting up drag and drop for 15 categories
categories.js?v=1751520785:612 [Categories] setupCategoryDragAndDrop called
categories.js?v=1751520785:615 [Categories] Removing old drag and drop listeners...
categories.js?v=1751520785:620 [Categories] Setting up drag and drop for 15 category rows
categories.js?v=1751520785:623 [Categories] Making category draggable: 0 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"258" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"258" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"258" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"258" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 1 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"275" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"275" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"275" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"275" data-parent-id=​"0" data-sort-order=​"1" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 2 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"170" data-parent-id=​"0" data-sort-order=​"2" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"170" data-parent-id=​"0" data-sort-order=​"2" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"170" data-parent-id=​"0" data-sort-order=​"2" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"170" data-parent-id=​"0" data-sort-order=​"2" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 3 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"171" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"171" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"171" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"171" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 4 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 5 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 6 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 7 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 8 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 9 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 10 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 11 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 12 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 13 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:623 [Categories] Making category draggable: 14 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751520785:627 [Categories] Drag and drop setup completed for all categories
categories.js?v=1751520785:59 [Categories] Initializing category hierarchy...
backend.js?v=1751386274:960 [BackendModule] Initializing category hierarchy...
backend.js?v=1751386274:958 [BackendModule] Category AJAX URLs loaded: {loadSubcategories: 'https://theme25.rakla.bg/admin/index.php?route=cat…ories&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT', updateSortOrder: 'https://theme25.rakla.bg/admin/index.php?route=cat…Order&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT', getCategoryInfo: 'https://theme25.rakla.bg/admin/index.php?route=cat…yInfo&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT'}
categories.js?v=1751520785:62 [Categories] Categories module initialization completed
backend.js?v=1751386274:960 [BackendModule] Categories module initialized successfully
backend.js?v=1751386274:958 [BackendModule] Expanding category: 177
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:644 [Categories] dragstart event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:691 [Categories] handleCategoryDragStart called with event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 435, screenY: 965, clientX: 435, …}
categories.js?v=1751520785:692 [Categories] Event target: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751520785:693 [Categories] Event currentTarget: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751520785:696 [Categories] Found draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
backend.js?v=1751386274:958 [BackendModule] draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751520785:708 [Categories] Got category ID: 247
categories.js?v=1751520785:724 [Categories] Created draggedData: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751520785:727 [Categories] Collapsing subcategories...
categories.js?v=1751520785:731 [Categories] Creating drag ghost...
categories.js?v=1751520785:757 [Categories] createDragGhost called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 435, screenY: 965, clientX: 435, …}
categories.js?v=1751520785:760 [Categories] Removing existing ghost...
categories.js?v=1751520785:764 [Categories] Cloning element...
categories.js?v=1751520785:768 [Categories] Ghost element created with ID and classes: category-drag-ghost category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all drag-ready category-drag-ghost
categories.js?v=1751520785:772 [Categories] Original element rect: DOMRect {x: 336, y: 803, width: 2161, height: 74, top: 803, …}
categories.js?v=1751520785:775 [Categories] Setting ghost styles...
categories.js?v=1751520785:793 [Categories] Ghost styles applied. Position: -645.5px 813px
categories.js?v=1751520785:796 [Categories] Appending ghost to body...
categories.js?v=1751520785:798 [Categories] Ghost appended to body. Ghost in DOM: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 2161px;​ height:​ 74px;​ left:​ -645.5px;​ top:​ 813px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751520785:802 [Categories] Ghost reference saved: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 2161px;​ height:​ 74px;​ left:​ -645.5px;​ top:​ 813px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751520785:805 [Categories] Adding mouse move listener...
categories.js?v=1751520785:888 [Categories] addDragMouseMoveListener called
categories.js?v=1751520785:896 [Categories] Mouse move listener added to document
categories.js?v=1751520785:808 [Categories] Drag ghost creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag ghost created
categories.js?v=1751520785:735 [Categories] Creating drag placeholder...
categories.js?v=1751520785:816 [Categories] createDragPlaceholder called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:830 [Categories] Placeholder element created: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751520785:831 [Categories] Placeholder styles: {height: '74px', backgroundColor: 'rgba(59, 130, 246, 0.1)', border: '2px dashed rgb(59, 130, 246)', display: 'block', visibility: 'visible'}
categories.js?v=1751520785:840 [Categories] Inserting placeholder before original element...
categories.js?v=1751520785:842 [Categories] Placeholder inserted. Placeholder in DOM: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751520785:845 [Categories] Hiding original element...
categories.js?v=1751520785:847 [Categories] Original element hidden. Display style: none
categories.js?v=1751520785:851 [Categories] Placeholder reference saved: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751520785:853 [Categories] Drag placeholder creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag placeholder created
categories.js?v=1751520785:739 [Categories] Adding visual effects...
categories.js?v=1751520785:744 [Categories] Setting transparent drag image...
categories.js?v=1751520785:749 [Categories] Drag start completed successfully
backend.js?v=1751386274:958 [BackendModule] Drag started for category: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751520785:648 [Categories] dragend event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751520785:903 [Categories] removeDragMouseMoveListener called
categories.js?v=1751520785:908 [Categories] Mouse move listener removed from document
backend.js?v=1751386274:960 [BackendModule] Drag ended