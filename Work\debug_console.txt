[Categories] Module loading...
categories.js?v=1751521691:29 [Categories] BackendModule is available for extension
categories.js?v=1751521691:30 [Categories] BackendModule type: object
categories.js?v=1751521691:31 [Categories] BackendModule object: {config: {…}, init: ƒ, initSidebar: ƒ, initModals: ƒ, initFilters: ƒ, …}
categories.js?v=1751521691:32 [Categories] Extending BackendModule with categories functionality...
categories.js?v=1751521691:1531 [Categories] BackendModule extended successfully
categories.js?v=1751521691:1536 [Categories] Module loaded
categories.js?v=1751521691:12 [Categories] DOMContentLoaded event fired
categories.js?v=1751521691:13 [Categories] Current page URL: https://theme25.rakla.bg/admin/index.php?route=catalog/category&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT
categories.js?v=1751521691:14 [Categories] Document ready state: interactive
categories.js?v=1751521691:18 [Categories] BackendModule found: {config: {…}, init: ƒ, initSidebar: ƒ, initModals: ƒ, initFilters: ƒ, …}
categories.js?v=1751521691:19 [Categories] Initializing categories...
categories.js?v=1751521691:45 [Categories] initCategories called
backend.js?v=1751386274:960 [BackendModule] Initializing categories module...
categories.js?v=1751521691:50 [Categories] Found category items: 15 NodeList(15) [div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…]
backend.js?v=1751386274:958 [BackendModule] Found category items: 15
categories.js?v=1751521691:53 [Categories] Setting up category event listeners...
categories.js?v=1751521691:56 [Categories] Initializing category drag and drop...
categories.js?v=1751521691:595 [Categories] initializeCategoryDragAndDrop called
categories.js?v=1751521691:598 [Categories] Found category rows: 15 NodeList(15) [div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…, div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.h…]
categories.js?v=1751521691:601 [Categories] Setting up drag and drop for 15 categories
categories.js?v=1751521691:612 [Categories] setupCategoryDragAndDrop called
categories.js?v=1751521691:615 [Categories] Removing old drag and drop listeners...
categories.js?v=1751521691:620 [Categories] Setting up drag and drop for 15 category rows
categories.js?v=1751521691:623 [Categories] Making category draggable: 0 div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:634 [Categories] Making category draggable: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:623 [Categories] Making category draggable: 1 div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:634 [Categories] Making category draggable: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:623 [Categories] Making category draggable: 2 div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:634 [Categories] Making category draggable: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:623 [Categories] Making category draggable: 3 div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:634 [Categories] Making category draggable: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all.drag-ready
categories.js?v=1751521691:623 [Categories] Making category draggable: 4 div.category-item.relative.flex.items-center.bg-white.border.border-gray-200.rounded-xl.shadow-sm.hover:shadow-md.transition-all
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"172" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 5 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"265" data-parent-id=​"0" data-sort-order=​"3" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 6 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"173" data-parent-id=​"0" data-sort-order=​"4" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 7 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"175" data-parent-id=​"0" data-sort-order=​"6" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 8 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"176" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 9 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"237" data-parent-id=​"0" data-sort-order=​"7" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 10 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"177" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0" data-has-subcategories=​"true">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 11 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"255" data-parent-id=​"0" data-sort-order=​"8" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 12 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"260" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 13 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"257" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:623 [Categories] Making category draggable: 14 <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" draggable=​"true" data-category-id=​"208" data-parent-id=​"0" data-sort-order=​"0" data-level=​"0">​…​</div>​flex
categories.js?v=1751521691:627 [Categories] Drag and drop setup completed for all categories
categories.js?v=1751521691:59 [Categories] Initializing category hierarchy...
backend.js?v=1751386274:960 [BackendModule] Initializing category hierarchy...
backend.js?v=1751386274:958 [BackendModule] Category AJAX URLs loaded: {loadSubcategories: 'https://theme25.rakla.bg/admin/index.php?route=cat…ories&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT', updateSortOrder: 'https://theme25.rakla.bg/admin/index.php?route=cat…Order&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT', getCategoryInfo: 'https://theme25.rakla.bg/admin/index.php?route=cat…yInfo&user_token=GxhAf5eZICBzN9V6rgqALnS7DgdnseBT'}
categories.js?v=1751521691:62 [Categories] Categories module initialization completed
backend.js?v=1751386274:960 [BackendModule] Categories module initialized successfully
backend.js?v=1751386274:958 [BackendModule] Expanding category: 177
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"209" data-level=​"1" data-parent-id=​"177" data-sort-order=​"1" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"210" data-level=​"1" data-parent-id=​"177" data-sort-order=​"2" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"211" data-level=​"1" data-parent-id=​"177" data-sort-order=​"3" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"212" data-level=​"1" data-parent-id=​"177" data-sort-order=​"4" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:634 [Categories] Making category draggable: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:640 [Categories] Set draggable=true and added drag-ready class to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:668 [Categories] All drag event listeners attached to: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:644 [Categories] dragstart event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:691 [Categories] handleCategoryDragStart called with event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 432, screenY: 957, clientX: 432, …}
categories.js?v=1751521691:692 [Categories] Event target: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:693 [Categories] Event currentTarget: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:696 [Categories] Found draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
backend.js?v=1751386274:958 [BackendModule] draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:708 [Categories] Got category ID: 247
categories.js?v=1751521691:724 [Categories] Created draggedData: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751521691:727 [Categories] Collapsing subcategories...
categories.js?v=1751521691:731 [Categories] Creating drag ghost...
categories.js?v=1751521691:757 [Categories] createDragGhost called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 432, screenY: 957, clientX: 432, …}
categories.js?v=1751521691:760 [Categories] Removing existing ghost...
categories.js?v=1751521691:764 [Categories] Cloning element...
categories.js?v=1751521691:768 [Categories] Ghost element created with ID and classes: category-drag-ghost category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all drag-ready category-drag-ghost
categories.js?v=1751521691:772 [Categories] Original element rect: DOMRect {x: 336, y: 803, width: 2161, height: 74, top: 803, …}
categories.js?v=1751521691:775 [Categories] Setting ghost styles...
categories.js?v=1751521691:783 [Categories] Ghost positioning - Original width: 2161 Limited width: 400
categories.js?v=1751521691:784 [Categories] Ghost positioning - Mouse at: 432 842 Ghost at: 232 805
categories.js?v=1751521691:803 [Categories] Ghost styles applied. Position: 232px 805px
categories.js?v=1751521691:806 [Categories] Appending ghost to body...
categories.js?v=1751521691:808 [Categories] Ghost appended to body. Ghost in DOM: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 232px;​ top:​ 805px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:812 [Categories] Ghost reference saved: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 232px;​ top:​ 805px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:815 [Categories] Adding mouse move listener...
categories.js?v=1751521691:898 [Categories] addDragMouseMoveListener called
categories.js?v=1751521691:906 [Categories] Mouse move listener added to document
categories.js?v=1751521691:818 [Categories] Drag ghost creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag ghost created
categories.js?v=1751521691:735 [Categories] Creating drag placeholder...
categories.js?v=1751521691:826 [Categories] createDragPlaceholder called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:840 [Categories] Placeholder element created: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:841 [Categories] Placeholder styles: {height: '74px', backgroundColor: 'rgba(59, 130, 246, 0.1)', border: '2px dashed rgb(59, 130, 246)', display: 'block', visibility: 'visible'}
categories.js?v=1751521691:850 [Categories] Inserting placeholder before original element...
categories.js?v=1751521691:852 [Categories] Placeholder inserted. Placeholder in DOM: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:855 [Categories] Hiding original element...
categories.js?v=1751521691:857 [Categories] Original element hidden. Display style: none
categories.js?v=1751521691:861 [Categories] Placeholder reference saved: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:863 [Categories] Drag placeholder creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag placeholder created
categories.js?v=1751521691:739 [Categories] Adding visual effects...
categories.js?v=1751521691:744 [Categories] Setting transparent drag image...
categories.js?v=1751521691:749 [Categories] Drag start completed successfully
backend.js?v=1751386274:958 [BackendModule] Drag started for category: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751521691:648 [Categories] dragend event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:913 [Categories] removeDragMouseMoveListener called
categories.js?v=1751521691:918 [Categories] Mouse move listener removed from document
backend.js?v=1751386274:960 [BackendModule] Drag ended
categories.js?v=1751521691:644 [Categories] dragstart event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:691 [Categories] handleCategoryDragStart called with event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 434, screenY: 956, clientX: 434, …}
categories.js?v=1751521691:692 [Categories] Event target: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:693 [Categories] Event currentTarget: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:696 [Categories] Found draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
backend.js?v=1751386274:958 [BackendModule] draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:708 [Categories] Got category ID: 247
categories.js?v=1751521691:724 [Categories] Created draggedData: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751521691:727 [Categories] Collapsing subcategories...
categories.js?v=1751521691:731 [Categories] Creating drag ghost...
categories.js?v=1751521691:757 [Categories] createDragGhost called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 434, screenY: 956, clientX: 434, …}
categories.js?v=1751521691:760 [Categories] Removing existing ghost...
categories.js?v=1751521691:764 [Categories] Cloning element...
categories.js?v=1751521691:768 [Categories] Ghost element created with ID and classes: category-drag-ghost category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all drag-ready category-drag-ghost
categories.js?v=1751521691:772 [Categories] Original element rect: DOMRect {x: 336, y: 807, width: 2161, height: 74, top: 807, …}
categories.js?v=1751521691:775 [Categories] Setting ghost styles...
categories.js?v=1751521691:783 [Categories] Ghost positioning - Original width: 2161 Limited width: 400
categories.js?v=1751521691:784 [Categories] Ghost positioning - Mouse at: 434 841 Ghost at: 234 804
categories.js?v=1751521691:803 [Categories] Ghost styles applied. Position: 234px 804px
categories.js?v=1751521691:806 [Categories] Appending ghost to body...
categories.js?v=1751521691:808 [Categories] Ghost appended to body. Ghost in DOM: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 234px;​ top:​ 804px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​" id=​"category-drag-ghost">​…​</div>​
categories.js?v=1751521691:812 [Categories] Ghost reference saved: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 234px;​ top:​ 804px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​" id=​"category-drag-ghost">​…​</div>​
categories.js?v=1751521691:815 [Categories] Adding mouse move listener...
categories.js?v=1751521691:898 [Categories] addDragMouseMoveListener called
categories.js?v=1751521691:906 [Categories] Mouse move listener added to document
categories.js?v=1751521691:818 [Categories] Drag ghost creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag ghost created
categories.js?v=1751521691:735 [Categories] Creating drag placeholder...
categories.js?v=1751521691:826 [Categories] createDragPlaceholder called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:840 [Categories] Placeholder element created: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:841 [Categories] Placeholder styles: {height: '74px', backgroundColor: 'rgba(59, 130, 246, 0.1)', border: '2px dashed rgb(59, 130, 246)', display: 'block', visibility: 'visible'}
categories.js?v=1751521691:850 [Categories] Inserting placeholder before original element...
categories.js?v=1751521691:852 [Categories] Placeholder inserted. Placeholder in DOM: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:855 [Categories] Hiding original element...
categories.js?v=1751521691:857 [Categories] Original element hidden. Display style: none
categories.js?v=1751521691:861 [Categories] Placeholder reference saved: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:863 [Categories] Drag placeholder creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag placeholder created
categories.js?v=1751521691:739 [Categories] Adding visual effects...
categories.js?v=1751521691:744 [Categories] Setting transparent drag image...
categories.js?v=1751521691:749 [Categories] Drag start completed successfully
backend.js?v=1751386274:958 [BackendModule] Drag started for category: {id: '247', parentId: '177', level: 1, name: 'За детето > Бебешко спално бельо - 100% Памук Поплин'}
categories.js?v=1751521691:648 [Categories] dragend event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"247" data-level=​"1" data-parent-id=​"177" data-sort-order=​"6" draggable=​"true" style>​…​</div>​flex
categories.js?v=1751521691:913 [Categories] removeDragMouseMoveListener called
categories.js?v=1751521691:918 [Categories] Mouse move listener removed from document
backend.js?v=1751386274:960 [BackendModule] Drag ended
categories.js?v=1751521691:644 [Categories] dragstart event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:691 [Categories] handleCategoryDragStart called with event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 437, screenY: 880, clientX: 437, …}
categories.js?v=1751521691:692 [Categories] Event target: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:693 [Categories] Event currentTarget: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:696 [Categories] Found draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
backend.js?v=1751386274:958 [BackendModule] draggedElement: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:708 [Categories] Got category ID: 213
categories.js?v=1751521691:724 [Categories] Created draggedData: {id: '213', parentId: '177', level: 1, name: 'За детето > Други'}
categories.js?v=1751521691:727 [Categories] Collapsing subcategories...
categories.js?v=1751521691:731 [Categories] Creating drag ghost...
categories.js?v=1751521691:757 [Categories] createDragGhost called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex event: DragEvent {isTrusted: true, dataTransfer: DataTransfer, screenX: 437, screenY: 880, clientX: 437, …}
categories.js?v=1751521691:760 [Categories] Removing existing ghost...
categories.js?v=1751521691:764 [Categories] Cloning element...
categories.js?v=1751521691:768 [Categories] Ghost element created with ID and classes: category-drag-ghost category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all drag-ready category-drag-ghost
categories.js?v=1751521691:772 [Categories] Original element rect: DOMRect {x: 336, y: 725, width: 2161, height: 74, top: 725, …}
categories.js?v=1751521691:775 [Categories] Setting ghost styles...
categories.js?v=1751521691:783 [Categories] Ghost positioning - Original width: 2161 Limited width: 400
categories.js?v=1751521691:784 [Categories] Ghost positioning - Mouse at: 437 765 Ghost at: 237 728
categories.js?v=1751521691:803 [Categories] Ghost styles applied. Position: 237px 728px
categories.js?v=1751521691:806 [Categories] Appending ghost to body...
categories.js?v=1751521691:808 [Categories] Ghost appended to body. Ghost in DOM: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 237px;​ top:​ 728px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:812 [Categories] Ghost reference saved: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready category-drag-ghost" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true" id=​"category-drag-ghost" style=​"position:​ fixed;​ z-index:​ 9999;​ pointer-events:​ none;​ width:​ 400px;​ height:​ 74px;​ left:​ 237px;​ top:​ 728px;​ opacity:​ 0.9;​ transform:​ rotate(3deg)​;​ box-shadow:​ rgba(0, 0, 0, 0.3)​ 0px 15px 35px;​ border:​ 2px solid rgb(59, 130, 246)​;​ background-color:​ white;​ border-radius:​ 12px;​ transition:​ none;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:815 [Categories] Adding mouse move listener...
categories.js?v=1751521691:898 [Categories] addDragMouseMoveListener called
categories.js?v=1751521691:906 [Categories] Mouse move listener added to document
categories.js?v=1751521691:818 [Categories] Drag ghost creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag ghost created
categories.js?v=1751521691:735 [Categories] Creating drag placeholder...
categories.js?v=1751521691:826 [Categories] createDragPlaceholder called with element: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:840 [Categories] Placeholder element created: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:841 [Categories] Placeholder styles: {height: '74px', backgroundColor: 'rgba(59, 130, 246, 0.1)', border: '2px dashed rgb(59, 130, 246)', display: 'block', visibility: 'visible'}
categories.js?v=1751521691:850 [Categories] Inserting placeholder before original element...
categories.js?v=1751521691:852 [Categories] Placeholder inserted. Placeholder in DOM: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:855 [Categories] Hiding original element...
categories.js?v=1751521691:857 [Categories] Original element hidden. Display style: none
categories.js?v=1751521691:861 [Categories] Placeholder reference saved: <div id=​"category-drag-placeholder" class=​"category-item category-drag-placeholder" style=​"height:​ 74px;​ background-color:​ rgba(59, 130, 246, 0.1)​;​ border:​ 2px dashed rgb(59, 130, 246)​;​ opacity:​ 0.7;​ min-height:​ 60px;​ display:​ block;​ visibility:​ visible;​">​…​</div>​
categories.js?v=1751521691:863 [Categories] Drag placeholder creation completed successfully
backend.js?v=1751386274:960 [BackendModule] Drag placeholder created
categories.js?v=1751521691:739 [Categories] Adding visual effects...
categories.js?v=1751521691:744 [Categories] Setting transparent drag image...
categories.js?v=1751521691:749 [Categories] Drag start completed successfully
backend.js?v=1751386274:958 [BackendModule] Drag started for category: {id: '213', parentId: '177', level: 1, name: 'За детето > Други'}
categories.js?v=1751521691:648 [Categories] dragend event fired on: <div class=​"category-item subcategory-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:​shadow-md transition-all drag-ready target-reset" data-category-id=​"213" data-level=​"1" data-parent-id=​"177" data-sort-order=​"5" draggable=​"true">​…​</div>​flex
categories.js?v=1751521691:913 [Categories] removeDragMouseMoveListener called
categories.js?v=1751521691:918 [Categories] Mouse move listener removed from document
backend.js?v=1751386274:960 [BackendModule] Drag ended